import { chromium, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>ontext } from 'playwright'
import { <PERSON>rowserWindow } from 'electron'

interface BotConfig {
  accountType: 'Demo' | 'Live'
  number: string
  search: string
}

interface BotStatus {
  isRunning: boolean
  isPaused: boolean
  currentUrl: string
  status: string
}

interface DomElement {
  tagName: string
  text?: string
  id?: string
  className?: string
  href?: string
  src?: string
}

class BotService {
  private browser: Browser | null = null
  private context: BrowserContext | null = null
  private page: Page | null = null
  private mainWindow: BrowserWindow | null = null
  private isRunning = false
  private isPaused = false
  private currentConfig: BotConfig | null = null
  private browserWindowPosition = { x: 0, y: 0, width: 1280, height: 720 }

  constructor(mainWindow: BrowserWindow) {
    this.mainWindow = mainWindow
  }

  async initialize(): Promise<{ success: boolean; error?: string }> {
    try {
      console.log('Initializing bot browser...')

      // Launch browser with visible UI and window positioning
      this.browser = await chromium.launch({
        headless: false, // Show the browser
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-web-security',
          '--disable-features=VizDisplayCompositor',
          `--window-position=${this.browserWindowPosition.x},${this.browserWindowPosition.y}`,
          `--window-size=${this.browserWindowPosition.width},${this.browserWindowPosition.height}`
        ]
      })

      // Create a new context with dynamic viewport size
      this.context = await this.browser.newContext({
        viewport: {
          width: this.browserWindowPosition.width,
          height: this.browserWindowPosition.height
        },
        userAgent:
          'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
      })

      // Create a new page
      this.page = await this.context.newPage()

      console.log('Bot browser initialized successfully')
      this.sendStatusUpdate({
        isRunning: false,
        isPaused: false,
        currentUrl: '',
        status: 'Browser initialized'
      })

      return { success: true }
    } catch (error) {
      console.error('Error initializing bot browser:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  async navigateToUrl(url: string): Promise<{ success: boolean; error?: string }> {
    try {
      if (!this.page) {
        throw new Error('Browser not initialized')
      }

      console.log(`Navigating to: ${url}`)
      await this.page.goto(url, { waitUntil: 'networkidle' })

      this.sendStatusUpdate({
        isRunning: this.isRunning,
        isPaused: this.isPaused,
        currentUrl: url,
        status: `Navigated to ${url}`
      })

      return { success: true }
    } catch (error) {
      console.error('Error navigating to URL:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  async queryDomElements(): Promise<{ success: boolean; elements?: DomElement[]; error?: string }> {
    try {
      if (!this.page) {
        throw new Error('Browser not initialized')
      }

      console.log('Querying DOM elements...')

      // Query various types of elements
      const elements = await this.page.evaluate(() => {
        const results: DomElement[] = []

        // Get all interactive elements
        const selectors = [
          'input',
          'button',
          'select',
          'textarea',
          'a',
          'h1',
          'h2',
          'h3',
          'h4',
          'h5',
          'h6',
          '[role="button"]',
          '[role="link"]',
          '[role="textbox"]'
        ]

        selectors.forEach((selector) => {
          const elements = document.querySelectorAll(selector)
          elements.forEach((el, index) => {
            if (index < 20) {
              // Limit to first 20 of each type
              const element = el as HTMLElement
              results.push({
                tagName: element.tagName.toLowerCase(),
                text: element.textContent?.trim().substring(0, 100) || '',
                id: element.id || undefined,
                className: element.className || undefined,
                href: (element as HTMLAnchorElement).href || undefined,
                src: (element as HTMLImageElement).src || undefined
              })
            }
          })
        })

        return results.filter((el) => el.text && el.text.length > 0)
      })

      console.log(`Found ${elements.length} DOM elements`)

      // Send elements to renderer
      this.mainWindow?.webContents.send('dom-elements-update', elements)

      return { success: true, elements }
    } catch (error) {
      console.error('Error querying DOM elements:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  async captureCanvasAsBase64(): Promise<{ success: boolean; data?: string; error?: string }> {
    try {
      if (!this.page) {
        throw new Error('Browser not initialized')
      }

      console.log('Capturing canvas.lot element as base64...')

      // Query for canvas.lot element and convert to base64
      const base64Data = await this.page.evaluate(() => {
        const canvas = document.querySelector('canvas.lot') as HTMLCanvasElement

        if (!canvas) {
          throw new Error('Canvas element with class "lot" not found')
        }

        // Convert canvas to base64 using toDataURL
        // Default format is PNG, but you can specify 'image/jpeg' for JPEG
        const dataUrl = canvas.toDataURL('image/png')

        // Return just the base64 data without the data:image/png;base64, prefix
        return dataUrl.split(',')[1]
      })

      console.log('Canvas captured successfully as base64')

      return { success: true, data: base64Data }
    } catch (error) {
      console.error('Error capturing canvas as base64:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  async startBot(config: BotConfig): Promise<{ success: boolean; error?: string }> {
    try {
      if (!this.page) {
        throw new Error('Browser not initialized')
      }

      this.currentConfig = config
      this.isRunning = true
      this.isPaused = false

      console.log('Starting bot with config:', config)

      this.sendStatusUpdate({
        isRunning: true,
        isPaused: false,
        currentUrl: this.page.url(),
        status: 'Bot started - executing tasks...'
      })

      // Execute bot tasks based on configuration
      await this.executeBotTasks(config)

      return { success: true }
    } catch (error) {
      console.error('Error starting bot:', error)
      this.isRunning = false
      this.sendStatusUpdate({
        isRunning: false,
        isPaused: false,
        currentUrl: this.page?.url() || '',
        status: 'Bot stopped due to error'
      })
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  async stopBot(): Promise<{ success: boolean; error?: string }> {
    try {
      this.isRunning = false
      this.isPaused = false

      console.log('Stopping bot...')

      this.sendStatusUpdate({
        isRunning: false,
        isPaused: false,
        currentUrl: this.page?.url() || '',
        status: 'Bot stopped'
      })

      return { success: true }
    } catch (error) {
      console.error('Error stopping bot:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  async pauseBot(paused: boolean): Promise<{ success: boolean; error?: string }> {
    try {
      this.isPaused = paused

      console.log(`Bot ${paused ? 'paused' : 'resumed'}`)

      this.sendStatusUpdate({
        isRunning: this.isRunning,
        isPaused: paused,
        currentUrl: this.page?.url() || '',
        status: paused ? 'Bot paused' : 'Bot resumed'
      })

      return { success: true }
    } catch (error) {
      console.error('Error pausing bot:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  private async executeBotTasks(config: BotConfig): Promise<void> {
    if (!this.page) return

    try {
      // Example bot tasks based on configuration
      console.log(
        `Executing bot tasks for ${config.accountType} account with number: ${config.number}`
      )

      this.sendStatusUpdate({
        isRunning: this.isRunning,
        isPaused: this.isPaused,
        currentUrl: this.page.url(),
        status: `Starting bot tasks...`
      })

      if (this.page.url().includes('localhost:5174')) {
        await this.performWebSearch(config.search)
      } else {
        // If not on Web, navigate there first
        this.sendStatusUpdate({
          isRunning: this.isRunning,
          isPaused: this.isPaused,
          currentUrl: this.page.url(),
          status: `Navigating to Web...`
        })

        await this.page.goto('http://localhost:5174', { waitUntil: 'networkidle' })
        await this.performWebSearch(config.search)
      }

      // Store config for potential future use in other methods
      if (this.currentConfig) {
        console.log(`Current config stored: ${JSON.stringify(this.currentConfig)}`)
      }

      this.sendStatusUpdate({
        isRunning: this.isRunning,
        isPaused: this.isPaused,
        currentUrl: this.page.url(),
        status: `Bot tasks completed successfully`
      })
    } catch (error) {
      console.error('Error executing bot tasks:', error)
      this.sendStatusUpdate({
        isRunning: this.isRunning,
        isPaused: this.isPaused,
        currentUrl: this.page?.url() || '',
        status: `Bot tasks failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      })
      throw error
    }
  }

  private async performWebSearch(searchTerm: string): Promise<void> {
    if (!this.page) return

    try {
      console.log(`Performing Web search for: ${searchTerm}`)

      this.sendStatusUpdate({
        isRunning: this.isRunning,
        isPaused: this.isPaused,
        currentUrl: this.page.url(),
        status: `Looking for search box...`
      })

      // Wait for search input to be available (try multiple selectors)
      const searchSelector = await this.page.waitForSelector(
        'input[name="q"], textarea[name="q"], input[title="Search"], input[aria-label*="Search"]',
        { timeout: 10000 }
      )

      if (!searchSelector) {
        throw new Error('Could not find search input')
      }

      this.sendStatusUpdate({
        isRunning: this.isRunning,
        isPaused: this.isPaused,
        currentUrl: this.page.url(),
        status: `Typing "${searchTerm}"...`
      })

      // Clear any existing text and type the search term
      await searchSelector.click()
      await this.page.keyboard.press('Control+A') // Select all existing text
      await this.page.keyboard.type(searchTerm, { delay: 100 }) // Type with delay to simulate human typing

      this.sendStatusUpdate({
        isRunning: this.isRunning,
        isPaused: this.isPaused,
        currentUrl: this.page.url(),
        status: `Clicking search button...`
      })

      // Wait a moment before searching
      await this.page.waitForTimeout(500)

      // Try to click the search button first, fallback to Enter key
      try {
        const searchButton = await this.page.waitForSelector(
          'input[value="Web Search"], button[aria-label="Web Search"], input[type="submit"]',
          { timeout: 3000 }
        )
        if (searchButton) {
          await searchButton.click()
        } else {
          // Fallback to pressing Enter
          await this.page.press('input[name="q"], textarea[name="q"]', 'Enter')
        }
      } catch {
        // If search button not found, press Enter
        await this.page.press('input[name="q"], textarea[name="q"]', 'Enter')
      }

      this.sendStatusUpdate({
        isRunning: this.isRunning,
        isPaused: this.isPaused,
        currentUrl: this.page.url(),
        status: `Waiting for search results...`
      })

      // Wait for results to load
      await this.page.waitForLoadState('networkidle', { timeout: 15000 })

      this.sendStatusUpdate({
        isRunning: this.isRunning,
        isPaused: this.isPaused,
        currentUrl: this.page.url(),
        status: `Search completed for: ${searchTerm}`
      })

      // Query new DOM elements after search
      await this.queryDomElements()
    } catch (error) {
      console.error('Error performing Web search:', error)
      this.sendStatusUpdate({
        isRunning: this.isRunning,
        isPaused: this.isPaused,
        currentUrl: this.page?.url() || '',
        status: `Search failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      })
      throw error
    }
  }

  private sendStatusUpdate(status: BotStatus): void {
    this.mainWindow?.webContents.send('bot-status-update', status)
  }

  async resizeBotBrowser(
    width: number,
    height: number,
    x: number = 0,
    y: number = 0
  ): Promise<{ success: boolean; error?: string }> {
    try {
      // Update browser window position and size
      this.browserWindowPosition = { x, y, width, height }

      console.log(`Repositioning bot browser to ${x},${y} with size ${width}x${height}`)

      // If browser is already initialized, we need to restart it with new position
      if (this.browser) {
        const currentUrl = this.page?.url() || ''
        console.log('Restarting browser with new position and size...')

        // Close existing browser
        await this.cleanup()

        // Reinitialize with new position
        const initResult = await this.initialize()
        if (!initResult.success) {
          return initResult
        }

        // Navigate back to the previous URL if it existed
        if (currentUrl && currentUrl !== 'about:blank') {
          await this.navigateToUrl(currentUrl)
        }
      }

      console.log(`Bot browser repositioned to ${x},${y} with size ${width}x${height}`)
      return { success: true }
    } catch (error) {
      console.error('Error repositioning bot browser:', error)
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
    }
  }

  async cleanup(): Promise<void> {
    try {
      if (this.page) {
        await this.page.close()
        this.page = null
      }
      if (this.context) {
        await this.context.close()
        this.context = null
      }
      if (this.browser) {
        await this.browser.close()
        this.browser = null
      }
      console.log('Bot service cleaned up')
    } catch (error) {
      console.error('Error cleaning up bot service:', error)
    }
  }
}

export default BotService
